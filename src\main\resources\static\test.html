<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #67c23a;
        }
        .error {
            border-left: 4px solid #f56c6c;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>知识管理系统 API 测试</h1>
        
        <!-- 知识库测试 -->
        <div class="test-section">
            <h3>知识库管理测试</h3>
            <button onclick="testGetKnowledgeBases()">获取知识库列表</button>
            <button onclick="testAddKnowledgeBase()">添加知识库</button>
            <div>
                <input type="text" id="knowledgeName" placeholder="知识库名称" value="测试知识库">
                <textarea id="knowledgeDesc" placeholder="知识库描述">这是一个测试知识库</textarea>
            </div>
            <div id="knowledgeResult" class="result"></div>
        </div>
        
        <!-- 文档测试 -->
        <div class="test-section">
            <h3>文档管理测试</h3>
            <button onclick="testGetDocuments()">获取文档列表</button>
            <div>
                <input type="number" id="pageNum" placeholder="页码" value="1">
                <input type="number" id="pageSize" placeholder="每页大小" value="10">
                <input type="text" id="categoryId" placeholder="类目ID（可选）">
            </div>
            <div id="documentResult" class="result"></div>
        </div>
        
        <!-- 系统信息 -->
        <div class="test-section">
            <h3>系统信息</h3>
            <button onclick="testSystemInfo()">检查系统状态</button>
            <div id="systemResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = '';
        
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }
        
        async function testGetKnowledgeBases() {
            try {
                const response = await fetch(`${API_BASE}/rag-knowledge/list`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                const result = await response.json();
                showResult('knowledgeResult', result, response.ok);
            } catch (error) {
                showResult('knowledgeResult', { error: error.message }, false);
            }
        }
        
        async function testAddKnowledgeBase() {
            const name = document.getElementById('knowledgeName').value;
            const description = document.getElementById('knowledgeDesc').value;
            
            if (!name.trim()) {
                showResult('knowledgeResult', { error: '请输入知识库名称' }, false);
                return;
            }
            
            try {
                const data = {
                    name: name,
                    description: description,
                    dataType: '00',
                    embeddingModel: 'text-embedding-ada-002',
                    topK: 5,
                    similarityThreshold: 0.7,
                    collectionName: `knowledge_${Date.now()}`
                };
                
                const response = await fetch(`${API_BASE}/rag-knowledge/add`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                showResult('knowledgeResult', result, response.ok);
            } catch (error) {
                showResult('knowledgeResult', { error: error.message }, false);
            }
        }
        
        async function testGetDocuments() {
            const pageNum = document.getElementById('pageNum').value || 1;
            const pageSize = document.getElementById('pageSize').value || 10;
            const categoryId = document.getElementById('categoryId').value;
            
            try {
                const params = {
                    pageNum: parseInt(pageNum),
                    pageSize: parseInt(pageSize)
                };
                
                if (categoryId) {
                    params.categoryId = categoryId;
                }
                
                const response = await fetch(`${API_BASE}/rag-document/page`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(params)
                });
                const result = await response.json();
                showResult('documentResult', result, response.ok);
            } catch (error) {
                showResult('documentResult', { error: error.message }, false);
            }
        }
        
        async function testSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                currentTime: new Date().toISOString(),
                location: window.location.href,
                apiBase: API_BASE || '当前域名',
                screenSize: `${screen.width}x${screen.height}`,
                windowSize: `${window.innerWidth}x${window.innerHeight}`
            };
            
            showResult('systemResult', info, true);
        }
        
        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', function() {
            testSystemInfo();
        });
    </script>
</body>
</html>
