package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 知识库文档响应
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档响应")
public class RagKnowledgeDocumentResponse {

    @Schema(description = "文档ID")
    private Long id;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件大小（MB）")
    private BigDecimal fileSize;

    @Schema(description = "处理状态（processing-处理中，completed-已完成，failed-处理失败）")
    private String status;

    @Schema(description = "切片数量")
    private Integer chunkCount;

    @Schema(description = "向量化状态")
    private String vectorizeStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "类目名称")
    private String categoryName;
}
