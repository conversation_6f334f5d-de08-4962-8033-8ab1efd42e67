package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.config.MilvusPoolConfig;
import com.faw.work.ais.aic.service.MilvusService;
import com.faw.work.ais.common.Response;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.collection.request.HasCollectionReq;
import io.milvus.v2.service.collection.response.ListCollectionsResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Milvus向量数据库操作控制器
 *
 * <AUTHOR>
 */
@Tag(name = "Milvus向量数据库操作控制器", description = "Milvus向量数据库操作控制器")
@Api(tags = "向量数据库管理")
@RestController
@RequestMapping("/milvus")
@RequiredArgsConstructor
@Slf4j
public class MilvusController {

    private final MilvusPoolConfig milvusPoolConfig;
    private final MilvusService milvusService;


    /**
     * 创建Milvus连接并检测集合是否存在
     *
     * @return 集合是否存在
     */
    @Operation(summary = "检测集合是否存在", description = "[author:10200571]")
    @ApiOperation("检测集合是否存在")
    @PostMapping("/check-collection")
    public Response<Boolean> checkCollection() {


        boolean exists = false;

        try {

            ConnectConfig connectConfig = ConnectConfig.builder()
                    .uri(String.format("%s:%d", milvusPoolConfig.getHost(), milvusPoolConfig.getPort()))
                    .username(milvusPoolConfig.getUsername())
                    .password(milvusPoolConfig.getPassword())
                    .dbName(milvusPoolConfig.getDbName())
                    .build();
            MilvusClientV2  client = new MilvusClientV2(connectConfig);

            // 检测集合是否存在
            Boolean lingxiaoxi = client.hasCollection(HasCollectionReq.builder().collectionName("lingxiaoxi").build());
            log.info("是否存在lingxiaoxi: {}", lingxiaoxi);
            // 关闭连接
            client.close();

        } catch (Exception e) {
            log.error("检测集合是否存在时发生错误: ", e);
            return Response.fail("检测集合时发生错误: " + e.getMessage());
        }

        return Response.success(exists);
    }

    /**
     * 获取所有Collection名称
     *
     * @return Collection名称列表
     */
    @Operation(summary = "获取所有Collection名称", description = "[author:wangxin213]")
    @GetMapping("/collections")
    public Response<List<String>> getCollections() {
        try {
            ConnectConfig connectConfig = ConnectConfig.builder()
                    .uri(String.format("%s:%d", milvusPoolConfig.getHost(), milvusPoolConfig.getPort()))
                    .username(milvusPoolConfig.getUsername())
                    .password(milvusPoolConfig.getPassword())
                    .dbName(milvusPoolConfig.getDbName())
                    .build();

            MilvusClientV2 client = new MilvusClientV2(connectConfig);

            // 获取所有collection名称
            ListCollectionsResp response = client.listCollections();
            List<String> collectionNames = response.getCollectionNames();

            log.info("获取到的Collection列表: {}", collectionNames);

            // 关闭连接
            client.close();

            return Response.success(collectionNames);

        } catch (Exception e) {
            log.error("获取Collection列表时发生错误: ", e);
            return Response.fail("获取Collection列表失败: " + e.getMessage());
        }
    }
}