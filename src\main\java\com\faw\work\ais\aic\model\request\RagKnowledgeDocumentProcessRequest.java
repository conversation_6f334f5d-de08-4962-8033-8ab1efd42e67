package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库文档处理请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档处理请求")
public class RagKnowledgeDocumentProcessRequest {

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long knowledgeId;

    @NotNull(message = "文档ID不能为空")
    @Schema(description = "文档ID", required = true)
    private Long documentId;
}
