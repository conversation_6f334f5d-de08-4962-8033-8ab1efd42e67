package com.faw.work.ais.aic.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.request.RagKnowledgeBasePageRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeCreateRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentBindRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentProcessRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentUnbindRequest;
import com.faw.work.ais.aic.model.request.RagKnowledgeDocumentsRequest;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentResponse;
import com.faw.work.ais.aic.service.RagKnowledgeDocumentJoinsService;
import com.faw.work.ais.aic.service.RagKnowledgeService;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.common.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * RAG知识库配置 控制器
 *
 * <AUTHOR> Assistant
 */
@Tag(name = "RAG知识库配置管理", description = "RAG知识库配置管理相关接口")
@RestController
@Slf4j
@RequestMapping("/rag-knowledge")
public class RagKnowledgeController {

    @Autowired
    private RagKnowledgeService knowledgeBaseService;

    @Autowired
    private RagKnowledgeDocumentJoinsService ragKnowledgeDocumentJoinsService;

    @Autowired
    private RagDocumentService ragDocumentService;

    @Autowired
    private com.faw.work.ais.aic.feign.DmsKnowledgeFeignClient dmsKnowledgeFeignClient;

    @Operation(summary = "根据ID查询知识库", description = "[author:10200571]")
    @PostMapping("/getById")
    public Response<RagKnowledgePO> getById(@RequestParam("id") Long id) {
        return Response.success(knowledgeBaseService.getById(id));
    }

    @Operation(summary = "根据名称查询知识库", description = "[author:10200571]")
    @PostMapping("/getByName")
    public Response<RagKnowledgePO> getByName(@RequestParam("name") String name) {
        return Response.success(knowledgeBaseService.getByName(name));
    }

    @Operation(summary = "查询知识库列表", description = "[author:10200571]")
    @PostMapping("/list")
    public Response<List<RagKnowledgePO>> list(@RequestBody(required = false) RagKnowledgePO knowledgeBase) {
        return Response.success(knowledgeBaseService.getKnowledgeBaseList(knowledgeBase));
    }

    @Operation(summary = "分页查询知识库", description = "[author:10200571]")
    @PostMapping("/page")
    public Response<Page<RagKnowledgePO>> page(@RequestBody RagKnowledgeBasePageRequest request) {
        return Response.success(knowledgeBaseService.getKnowledgeBasePage(request));
    }

    @Operation(summary = "新增知识库", description = "[author:10200571]")
    @PostMapping("/add")
    public Response<Boolean> add(@RequestBody RagKnowledgePO knowledgeBase) {
        // 检查名称是否已存在
        RagKnowledgePO existingBase = knowledgeBaseService.getByName(knowledgeBase.getName());
        if (existingBase != null) {
            return Response.fail("知识库名称已存在");
        }
        
        if (knowledgeBase.getCreatedAt() == null) {
            knowledgeBase.setCreatedAt(LocalDateTime.now());
        }
        if (knowledgeBase.getUpdatedAt() == null) {
            knowledgeBase.setUpdatedAt(LocalDateTime.now());
        }
        return Response.success(knowledgeBaseService.save(knowledgeBase));
    }

    @Operation(summary = "更新知识库", description = "[author:10200571]")
    @PostMapping("/update")
    public Response<Boolean> update(@RequestBody RagKnowledgePO knowledgeBase) {
        // 检查名称是否已存在且不是当前记录
        RagKnowledgePO existingBase = knowledgeBaseService.getByName(knowledgeBase.getName());
        if (existingBase != null && !existingBase.getId().equals(knowledgeBase.getId())) {
            return Response.fail("知识库名称已存在");
        }

        knowledgeBase.setUpdatedAt(LocalDateTime.now());
        return Response.success(knowledgeBaseService.updateById(knowledgeBase));
    }

    @Operation(summary = "删除知识库", description = "[author:10200571]")
    @PostMapping("/delete")
    public Response<Boolean> delete(@RequestParam("id") Long id) {
        return Response.success(knowledgeBaseService.removeById(id));
    }

    @Operation(summary = "解绑知识库和文档关系", description = "[author:10200571]")
    @PostMapping("/unbind-documents")
    public Response<RagKnowledgeDocumentBindResponse> unbindDocuments(@Valid @RequestBody RagKnowledgeDocumentUnbindRequest request) {
        log.info("解绑知识库文档关系请求: {}", request);

        try {
            RagKnowledgeDocumentBindResponse response = ragKnowledgeDocumentJoinsService.unbindDocuments(request);

            if (response.getFailCount() > 0) {
                log.warn("部分文档解绑失败: 成功={}, 失败={}", response.getSuccessCount(), response.getFailCount());
            }

            return Response.success(response);

        } catch (Exception e) {
            log.error("解绑知识库文档关系失败", e);
            return Response.fail("解绑失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询知识库绑定的文档列表", description = "[author:10200571]")
    @PostMapping("/bound-documents")
    public Response<List<Long>> getBoundDocuments(@RequestParam("ragKnowledgeId") Long ragKnowledgeId) {
        log.info("查询知识库绑定的文档: ragKnowledgeId={}", ragKnowledgeId);

        try {
            List<Long> documentIds = ragKnowledgeDocumentJoinsService.getByBaseId(ragKnowledgeId)
                .stream()
                .map(join -> join.getDocumentId())
                .toList();

            return Response.success(documentIds);

        } catch (Exception e) {
            log.error("查询知识库绑定文档失败", e);
            return Response.fail("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建知识库", description = "[author:wangxin213]")
    @PostMapping("/create")
    public Response<RagKnowledgePO> createKnowledge(@Valid @RequestBody RagKnowledgeCreateRequest request) {
        log.info("创建知识库请求: {}", request);

        try {
            // 检查名称是否已存在
            RagKnowledgePO existingKnowledge = knowledgeBaseService.getByName(request.getName());
            if (existingKnowledge != null) {
                return Response.fail("知识库名称已存在");
            }

            // 创建知识库对象
            RagKnowledgePO knowledge = new RagKnowledgePO();
            BeanUtils.copyProperties(request, knowledge);
            knowledge.setCreatedAt(LocalDateTime.now());
            knowledge.setUpdatedAt(LocalDateTime.now());
            knowledge.setCreatedBy("system"); // 这里可以从上下文获取当前用户

            // 保存知识库
            boolean success = knowledgeBaseService.save(knowledge);
            if (success) {
                return Response.success(knowledge);
            } else {
                return Response.fail("创建知识库失败");
            }

        } catch (Exception e) {
            log.error("创建知识库失败", e);
            return Response.fail("创建失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询知识库关联的文档详情", description = "[author:wangxin213]")
    @PostMapping("/documents")
    public Response<Page<RagKnowledgeDocumentResponse>> getKnowledgeDocuments(@Valid @RequestBody RagKnowledgeDocumentsRequest request) {
        log.info("查询知识库文档请求: {}", request);

        try {
            // 这里需要实现具体的查询逻辑，暂时返回空结果
            Page<RagKnowledgeDocumentResponse> page = new Page<>(request.getPageNum(), request.getPageSize());
            return Response.success(page);

        } catch (Exception e) {
            log.error("查询知识库文档失败", e);
            return Response.fail("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "绑定文档到知识库", description = "[author:wangxin213]")
    @PostMapping("/bind-documents")
    public Response<RagKnowledgeDocumentBindResponse> bindDocuments(@Valid @RequestBody RagKnowledgeDocumentBindRequest request) {
        log.info("绑定文档到知识库请求: {}", request);

        try {
            RagKnowledgeDocumentBindResponse response = ragKnowledgeDocumentJoinsService.bindDocuments(request);
            return Response.success(response);

        } catch (Exception e) {
            log.error("绑定文档到知识库失败", e);
            return Response.fail("绑定失败: " + e.getMessage());
        }
    }

    @Operation(summary = "从知识库解绑文档", description = "[author:wangxin213]")
    @PostMapping("/unbind-document")
    public Response<Boolean> unbindDocument(@Valid @RequestBody RagKnowledgeDocumentProcessRequest request) {
        log.info("从知识库解绑文档请求: {}", request);

        try {
            // 构造解绑请求
            RagKnowledgeDocumentUnbindRequest unbindRequest = new RagKnowledgeDocumentUnbindRequest();
            unbindRequest.setRagKnowledgeId(request.getKnowledgeId());
            unbindRequest.setDocumentIds(List.of(request.getDocumentId()));

            RagKnowledgeDocumentBindResponse response = ragKnowledgeDocumentJoinsService.unbindDocuments(unbindRequest);
            return Response.success(response.getSuccessCount() > 0);

        } catch (Exception e) {
            log.error("从知识库解绑文档失败", e);
            return Response.fail("解绑失败: " + e.getMessage());
        }
    }

    @Operation(summary = "处理知识库文档", description = "[author:wangxin213]")
    @PostMapping("/process-document")
    public Response<String> processDocument(@Valid @RequestBody RagKnowledgeDocumentProcessRequest request) {
        log.info("处理知识库文档请求: {}", request);

        try {
            // 这里需要实现文档处理逻辑，比如重新切分、向量化等
            // 暂时返回成功消息
            return Response.success("文档处理任务已提交，请稍后查看处理结果");

        } catch (Exception e) {
            log.error("处理知识库文档失败", e);
            return Response.fail("处理失败: " + e.getMessage());
        }
    }

}