<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用数据 - 知识管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.2/dist/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">

</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 顶部导航 -->
            <div class="header">
                <div class="header-title">应用数据</div>
                <div class="header-nav">
                    <a href="#" class="nav-item active">非结构化数据</a>
                </div>
                <div>
                    <el-button type="text" icon="QuestionFilled">使用指南</el-button>
                </div>
            </div>
            
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 左侧标签页导航 -->
                <div class="sidebar">
                    <el-tabs v-model="activeTab" tab-position="left" class="sidebar-tabs">
                        <el-tab-pane label="数据管理" name="dataManagement">
                            <div class="tab-content">
                                <div class="sidebar-title">
                                    类目管理
                                    <el-button type="text" icon="Plus" size="small" @click="showAddCategoryDialog = true" style="float: right;"></el-button>
                                </div>
                                <ul class="category-list">
                                    <li v-for="category in categories" :key="category.id"
                                        :class="['category-item', { active: selectedCategory?.id === category.id }]"
                                        @click="selectCategory(category)">
                                        <span class="category-name">{{ category.name }}</span>
                                        <div class="category-actions">
                                            <el-button type="text" icon="Edit" size="small" @click.stop="editCategory(category)"></el-button>
                                            <el-button type="text" icon="Delete" size="small" @click.stop="deleteCategory(category)"></el-button>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="知识库管理" name="knowledgeManagement">
                            <div class="tab-content">
                                <div class="sidebar-title">
                                    知识库列表
                                    <el-button type="primary" icon="Plus" size="small" class="add-knowledge-btn" @click="showCreateKnowledgeDialog = true">新建</el-button>
                                </div>
                                <div class="knowledge-search">
                                    <el-input
                                        v-model="knowledgeSearchKeyword"
                                        placeholder="搜索知识库名称"
                                        size="small"
                                        clearable
                                        @input="searchKnowledgeBases">
                                        <template #prefix>
                                            <el-icon><Search /></el-icon>
                                        </template>
                                    </el-input>
                                </div>
                                <ul class="knowledge-list">
                                    <li v-for="knowledge in filteredKnowledgeBases" :key="knowledge.id"
                                        :class="['knowledge-item', { active: selectedKnowledge?.id === knowledge.id }]"
                                        @click="selectKnowledge(knowledge)">
                                        <div class="knowledge-info">
                                            <div class="knowledge-name">{{ knowledge.name }}</div>
                                            <div class="knowledge-description" v-if="knowledge.description">{{ knowledge.description }}</div>
                                            <div class="knowledge-meta">
                                                <div class="knowledge-meta-item">
                                                    <span class="knowledge-meta-label">文档:</span>
                                                    <span class="knowledge-meta-value">{{ getKnowledgeDocCount(knowledge.id) }}个</span>
                                                </div>
                                                <div class="knowledge-meta-item">
                                                    <span class="knowledge-meta-label">类型:</span>
                                                    <span class="knowledge-meta-value">{{ knowledge.dataType === '00' ? '非结构化' : '结构化' }}</span>
                                                </div>
                                                <div class="knowledge-meta-item">
                                                    <span class="knowledge-meta-label">更新:</span>
                                                    <span class="knowledge-meta-value">{{ formatDate(knowledge.updatedAt) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="knowledge-actions">
                                            <el-button type="text" icon="Setting" size="small" @click.stop="configKnowledge(knowledge)" title="配置"></el-button>
                                            <el-button type="text" icon="Edit" size="small" @click.stop="editKnowledge(knowledge)" title="编辑"></el-button>
                                            <el-button type="text" icon="Delete" size="small" @click.stop="deleteKnowledge(knowledge)" title="删除"></el-button>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>

                <!-- 右侧内容区域 -->
                <div class="content-area">
                    <!-- 数据管理内容 -->
                    <div v-if="activeTab === 'dataManagement'" class="data-management-content">
                        <!-- 搜索和操作栏 -->
                        <div class="content-header">
                            <div class="search-bar">
                                <span style="font-weight: 600;">{{ selectedCategory?.name || '请选择类目' }}</span>
                                <el-icon><InfoFilled /></el-icon>
                                <span style="color: #909399;">共{{ totalDocuments }}个文件 | {{ processedDocuments }}个文件解析中</span>

                                <el-input
                                    v-model="searchKeyword"
                                    placeholder="搜索文件名称"
                                    style="width: 200px;"
                                    clearable>
                                </el-input>

                                <el-select v-model="filterType" placeholder="全部" style="width: 120px;">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="PDF" value="pdf"></el-option>
                                    <el-option label="Word" value="docx"></el-option>
                                    <el-option label="Excel" value="xlsx"></el-option>
                                </el-select>

                                <el-button icon="Search" @click="searchDocuments">搜索</el-button>
                                <el-button icon="Refresh" @click="refreshDocuments"></el-button>
                            </div>

                            <div>
                                <el-button type="text">数据解析设置</el-button>
                                <el-button type="text">批量管理</el-button>
                                <el-button type="primary" icon="Upload" @click="showUploadDialogHandler">导入数据</el-button>
                            </div>
                        </div>

                        <!-- 文档表格 -->
                        <div class="document-table">
                            <el-table
                                :data="documents"
                                v-loading="loading"
                                style="width: 100%"
                                @selection-change="handleSelectionChange">
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column prop="fileName" label="文件名称" min-width="200">
                                    <template #default="scope">
                                        <div style="display: flex; align-items: center;">
                                            <el-icon class="file-icon" color="#f56565" v-if="scope.row.fileType === 'pdf'">
                                                <Document />
                                            </el-icon>
                                            <el-icon class="file-icon" color="#3182ce" v-else-if="scope.row.fileType === 'docx'">
                                                <Document />
                                            </el-icon>
                                            <el-icon class="file-icon" color="#38a169" v-else>
                                                <Document />
                                            </el-icon>
                                            <span>{{ scope.row.fileName }}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fileType" label="文件格式" width="100">
                                    <template #default="scope">
                                        <span style="text-transform: uppercase;">{{ scope.row.fileType }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fileSize" label="文件大小" width="120">
                                    <template #default="scope">
                                        {{ formatFileSize(scope.row.fileSize) }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="parseStatus" label="状态" width="120">
                                    <template #default="scope">
                                        <span :class="getStatusClass(scope.row.parseStatus)">
                                            {{ getStatusText(scope.row.parseStatus) }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="vectorizeStatus" label="数据来源" width="120">
                                    <template #default="scope">
                                        {{ scope.row.vectorizeStatus === '01' ? '本地导入' : '知识中心' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createdAt" label="上传时间" width="180">
                                    <template #default="scope">
                                        {{ formatDate(scope.row.createdAt) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120" fixed="right">
                                    <template #default="scope">
                                        <div class="action-buttons">
                                            <el-button type="text" icon="Download" size="small" @click="downloadDocument(scope.row)"></el-button>
                                            <el-button type="text" icon="Delete" size="small" @click="deleteDocument(scope.row)"></el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div style="padding: 20px; text-align: center;">
                                <el-pagination
                                    v-model:current-page="currentPage"
                                    v-model:page-size="pageSize"
                                    :page-sizes="[10, 20, 50, 100]"
                                    :total="totalDocuments"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange">
                                </el-pagination>
                            </div>
                        </div>
                    </div>

                    <!-- 知识库管理内容 -->
                    <div v-else-if="activeTab === 'knowledgeManagement'" class="knowledge-management-content">
                        <!-- 知识库详情 -->
                        <div v-if="selectedKnowledge" class="knowledge-detail">
                            <div class="knowledge-header">
                                <div class="knowledge-title">
                                    <h2>{{ selectedKnowledge.name }}</h2>
                                    <div class="knowledge-meta">
                                        <span>{{ getKnowledgeDocCount(selectedKnowledge.id) }}个文档</span>
                                        <span>更新时间：{{ formatDate(selectedKnowledge.updatedAt) }}</span>
                                    </div>
                                </div>
                                <div class="knowledge-actions">
                                    <el-button @click="configKnowledge(selectedKnowledge)">配置</el-button>
                                    <el-button @click="editKnowledge(selectedKnowledge)">编辑</el-button>
                                    <el-button type="danger" @click="deleteKnowledge(selectedKnowledge)">删除</el-button>
                                </div>
                            </div>

                            <!-- 知识库文档列表 -->
                            <div class="knowledge-documents">
                                <div class="documents-header">
                                    <h3>关联文档</h3>
                                    <el-button type="primary" @click="showBindDocumentsDialog = true">绑定文档</el-button>
                                </div>
                                <el-table :data="knowledgeDocuments" style="width: 100%">
                                    <el-table-column prop="fileName" label="文件名" min-width="200"></el-table-column>
                                    <el-table-column prop="fileType" label="文件类型" width="100">
                                        <template #default="scope">
                                            <el-tag size="small">{{ scope.row.fileType.toUpperCase() }}</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" label="状态" width="120">
                                        <template #default="scope">
                                            <el-tag :type="getKnowledgeDocStatusType(scope.row.status)" size="small">
                                                {{ getKnowledgeDocStatusText(scope.row.status) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="chunkCount" label="切片数量" width="100"></el-table-column>
                                    <el-table-column prop="updatedAt" label="更新时间" width="180">
                                        <template #default="scope">
                                            {{ formatDate(scope.row.updatedAt) }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="150">
                                        <template #default="scope">
                                            <el-button type="text" size="small" @click="processDocument(scope.row)">重新处理</el-button>
                                            <el-button type="text" size="small" @click="unbindDocument(scope.row)">解绑</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>

                        <!-- 空状态 -->
                        <div v-else class="empty-state">
                            <el-empty description="请选择知识库">
                                <el-button type="primary" @click="showCreateKnowledgeDialog = true">创建知识库</el-button>
                            </el-empty>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 添加类目对话框 -->
        <el-dialog v-model="showAddCategoryDialog" title="添加类目" width="500px">
            <el-form :model="categoryForm" label-width="80px">
                <el-form-item label="类目名称" required>
                    <el-input v-model="categoryForm.name" placeholder="请输入类目名称"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input v-model="categoryForm.description" type="textarea" placeholder="请输入类目描述"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showAddCategoryDialog = false">取消</el-button>
                <el-button type="primary" @click="addCategory">确定</el-button>
            </template>
        </el-dialog>
        
        <!-- 编辑类目对话框 -->
        <el-dialog v-model="showEditCategoryDialog" title="编辑类目" width="500px">
            <el-form :model="editCategoryForm" label-width="80px">
                <el-form-item label="类目名称" required>
                    <el-input v-model="editCategoryForm.name" placeholder="请输入类目名称"></el-input>
                </el-form-item>
                <el-form-item label="描述">
                    <el-input v-model="editCategoryForm.description" type="textarea" placeholder="请输入类目描述"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showEditCategoryDialog = false">取消</el-button>
                <el-button type="primary" @click="updateCategory">确定</el-button>
            </template>
        </el-dialog>
        
        <!-- 上传文档对话框 -->
        <el-dialog v-model="showUploadDialog" title="上传文档" width="600px">
            <el-form :model="uploadForm" label-width="100px">
                <el-form-item label="选择类目" required>
                    <el-select v-model="uploadForm.categoryId" placeholder="请选择类目" style="width: 100%;">
                        <el-option 
                            v-for="category in categories" 
                            :key="category.id" 
                            :label="category.name" 
                            :value="category.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="选择知识库" required>
                    <el-select v-model="uploadForm.ragKnowledgeId" placeholder="请选择知识库" style="width: 100%;">
                        <el-option 
                            v-for="knowledge in knowledgeBases" 
                            :key="knowledge.id" 
                            :label="knowledge.name" 
                            :value="knowledge.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="上传文件" required>
                    <el-upload
                        ref="uploadRef"
                        :auto-upload="false"
                        :on-change="handleFileChange"
                        :file-list="fileList"
                        :limit="1"
                        accept=".pdf,.doc,.docx,.txt">
                        <el-button type="primary" icon="Upload">选择文件</el-button>
                        <template #tip>
                            <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                                支持 PDF、Word、TXT 格式，单个文件不超过 50MB
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showUploadDialog = false">取消</el-button>
                <el-button type="primary" @click="uploadDocument" :loading="uploading">上传</el-button>
            </template>
        </el-dialog>

        <!-- 创建知识库对话框 -->
        <el-dialog v-model="showCreateKnowledgeDialog" title="创建知识库" width="800px">
            <el-form :model="knowledgeForm" :rules="knowledgeRules" ref="knowledgeFormRef" label-width="120px">
                <el-form-item label="知识库名称" prop="name">
                    <el-input v-model="knowledgeForm.name" placeholder="请输入知识库名称" maxlength="50"></el-input>
                </el-form-item>
                <el-form-item label="知识库描述" prop="description">
                    <el-input
                        v-model="knowledgeForm.description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入知识库描述，可以描述知识库的用途、内容等"
                        maxlength="200">
                    </el-input>
                </el-form-item>

                <!-- 数据源配置 -->
                <el-divider content-position="left">数据源配置</el-divider>
                <el-form-item label="数据源类型" prop="dataSourceType">
                    <el-radio-group v-model="knowledgeForm.dataSourceType">
                        <el-radio label="local">本地数据源</el-radio>
                        <el-radio label="external">外部数据源</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 切分策略配置 -->
                <el-divider content-position="left">切分策略配置</el-divider>
                <el-form-item label="切分方式" prop="chunkStrategy">
                    <el-radio-group v-model="knowledgeForm.chunkStrategy">
                        <el-radio label="smart">智能切分</el-radio>
                        <el-radio label="fixed">固定长度</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item v-if="knowledgeForm.chunkStrategy === 'fixed'" label="最大切片长度" prop="maxChunkSize">
                    <el-slider
                        v-model="knowledgeForm.maxChunkSize"
                        :min="100"
                        :max="4000"
                        :step="100"
                        show-input>
                    </el-slider>
                </el-form-item>

                <el-form-item label="切片重叠长度" prop="chunkOverlap">
                    <el-slider
                        v-model="knowledgeForm.chunkOverlap"
                        :min="0"
                        :max="400"
                        :step="10"
                        show-input>
                    </el-slider>
                </el-form-item>

                <!-- 向量化配置 -->
                <el-divider content-position="left">向量化配置</el-divider>
                <el-form-item label="向量化模型" prop="embeddingModel">
                    <el-select v-model="knowledgeForm.embeddingModel" placeholder="请选择向量化模型">
                        <el-option label="text-embedding-v2" value="text-embedding-v2"></el-option>
                        <el-option label="text-embedding-v3" value="text-embedding-v3"></el-option>
                        <el-option label="text-embedding-v4" value="text-embedding-v4"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="Collection" prop="collectionName">
                    <el-select v-model="knowledgeForm.collectionName" placeholder="请选择Collection" @focus="loadCollections">
                        <el-option
                            v-for="collection in collections"
                            :key="collection"
                            :label="collection"
                            :value="collection">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="相似度阈值" prop="similarityThreshold">
                    <el-slider
                        v-model="knowledgeForm.similarityThreshold"
                        :min="0.1"
                        :max="1.0"
                        :step="0.01"
                        show-input>
                    </el-slider>
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showCreateKnowledgeDialog = false">取消</el-button>
                    <el-button type="primary" @click="createKnowledge">创建知识库</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 绑定文档对话框 -->
        <el-dialog v-model="showBindDocumentsDialog" title="绑定文档" width="800px">
            <div class="bind-documents-content">
                <div class="documents-filter">
                    <el-input
                        v-model="bindDocumentSearchKeyword"
                        placeholder="搜索文档名称"
                        style="width: 300px;"
                        clearable>
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                    <el-select v-model="bindDocumentCategoryFilter" placeholder="选择类目" style="width: 200px;">
                        <el-option label="全部类目" value=""></el-option>
                        <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id"></el-option>
                    </el-select>
                </div>

                <el-table
                    :data="availableDocuments"
                    @selection-change="handleBindDocumentSelectionChange"
                    style="width: 100%; margin-top: 20px;">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="fileName" label="文件名" min-width="200"></el-table-column>
                    <el-table-column prop="fileType" label="类型" width="80">
                        <template #default="scope">
                            <el-tag size="small">{{ scope.row.fileType.toUpperCase() }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fileSize" label="大小" width="100">
                        <template #default="scope">
                            {{ formatFileSize(scope.row.fileSize) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="categoryName" label="类目" width="120"></el-table-column>
                    <el-table-column prop="createdAt" label="上传时间" width="180">
                        <template #default="scope">
                            {{ formatDate(scope.row.createdAt) }}
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showBindDocumentsDialog = false">取消</el-button>
                    <el-button type="primary" @click="bindDocuments">绑定选中文档</el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.2/dist/index.full.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@element-plus/icons-vue@2.1.0/dist/index.iife.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
