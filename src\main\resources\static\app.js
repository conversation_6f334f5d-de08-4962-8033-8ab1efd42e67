const { createApp, ref, reactive, onMounted, computed } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

const app = createApp({
    setup() {
        // 响应式数据
        const loading = ref(false);
        const uploading = ref(false);

        // 标签页管理
        const activeTab = ref('dataManagement');

        // 类目相关
        const categories = ref([]);
        const selectedCategory = ref(null);
        const showAddCategoryDialog = ref(false);
        const showEditCategoryDialog = ref(false);
        const categoryForm = reactive({
            name: '',
            description: ''
        });
        const editCategoryForm = reactive({
            id: null,
            name: '',
            description: ''
        });

        // 知识库相关
        const knowledgeBases = ref([]);
        const selectedKnowledge = ref(null);
        const knowledgeSearchKeyword = ref('');
        const showCreateKnowledgeDialog = ref(false);
        const collections = ref([]);
        const knowledgeForm = reactive({
            name: '',
            description: '',
            dataSourceType: 'local',
            chunkStrategy: 'smart',
            maxChunkSize: 1000,
            chunkOverlap: 100,
            embeddingModel: 'text-embedding-v2',
            collectionName: '',
            similarityThreshold: 0.7
        });
        const knowledgeRules = {
            name: [
                { required: true, message: '请输入知识库名称', trigger: 'blur' },
                { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
            ],
            description: [
                { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
            ]
        };

        // 知识库文档相关
        const knowledgeDocuments = ref([]);
        const showBindDocumentsDialog = ref(false);
        const availableDocuments = ref([]);
        const selectedBindDocuments = ref([]);
        const bindDocumentSearchKeyword = ref('');
        const bindDocumentCategoryFilter = ref('');
        
        // 文档相关
        const documents = ref([]);
        const selectedDocuments = ref([]);
        const showUploadDialog = ref(false);
        const uploadForm = reactive({
            categoryId: null,
            ragKnowledgeId: null
        });
        const fileList = ref([]);
        
        // 搜索和分页
        const searchKeyword = ref('');
        const filterType = ref('');
        const currentPage = ref(1);
        const pageSize = ref(10);
        const totalDocuments = ref(0);
        
        // 计算属性
        const processedDocuments = computed(() => {
            return documents.value.filter(doc => doc.parseStatus === '01').length;
        });

        const filteredKnowledgeBases = computed(() => {
            if (!knowledgeSearchKeyword.value) {
                return knowledgeBases.value;
            }
            return knowledgeBases.value.filter(kb =>
                kb.name.toLowerCase().includes(knowledgeSearchKeyword.value.toLowerCase())
            );
        });
        
        // API 基础配置
        const API_BASE = '';
        
        // API 调用函数
        const api = {
            // 知识库相关API
            async getKnowledgeBases() {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/list`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({})
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data || [];
                    }
                    throw new Error(result.message || '获取知识库列表失败');
                } catch (error) {
                    console.error('获取知识库列表失败:', error);
                    ElMessage.error('获取知识库列表失败');
                    return [];
                }
            },
            
            async addKnowledgeBase(data) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/add`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '添加知识库失败');
                } catch (error) {
                    console.error('添加知识库失败:', error);
                    throw error;
                }
            },
            
            async updateKnowledgeBase(data) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/update`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '更新知识库失败');
                } catch (error) {
                    console.error('更新知识库失败:', error);
                    throw error;
                }
            },
            
            async deleteKnowledgeBase(id) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/delete?id=${id}`, {
                        method: 'POST'
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '删除知识库失败');
                } catch (error) {
                    console.error('删除知识库失败:', error);
                    throw error;
                }
            },

            // 新增知识库管理API
            async createKnowledge(data) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/create`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '创建知识库失败');
                } catch (error) {
                    console.error('创建知识库失败:', error);
                    throw error;
                }
            },

            async deleteKnowledge(id) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/delete?id=${id}`, {
                        method: 'POST'
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '删除知识库失败');
                } catch (error) {
                    console.error('删除知识库失败:', error);
                    throw error;
                }
            },

            async getKnowledgeDocuments(knowledgeId) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/documents`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ knowledgeId })
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result;
                    }
                    throw new Error(result.message || '获取知识库文档失败');
                } catch (error) {
                    console.error('获取知识库文档失败:', error);
                    throw error;
                }
            },

            async bindDocumentsToKnowledge(knowledgeId, documentIds) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/bind-documents`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ knowledgeId, documentIds })
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '绑定文档失败');
                } catch (error) {
                    console.error('绑定文档失败:', error);
                    throw error;
                }
            },

            async unbindDocumentFromKnowledge(knowledgeId, documentId) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/unbind-document`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ knowledgeId, documentId })
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '解绑文档失败');
                } catch (error) {
                    console.error('解绑文档失败:', error);
                    throw error;
                }
            },

            async processKnowledgeDocument(knowledgeId, documentId) {
                try {
                    const response = await fetch(`${API_BASE}/rag-knowledge/process-document`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ knowledgeId, documentId })
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '处理文档失败');
                } catch (error) {
                    console.error('处理文档失败:', error);
                    throw error;
                }
            },
            
            // 文档相关API
            async getDocuments(params = {}) {
                try {
                    const queryParams = {
                        pageNum: currentPage.value,
                        pageSize: pageSize.value,
                        ...params
                    };

                    const response = await fetch(`${API_BASE}/rag-document/page`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(queryParams)
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '获取文档列表失败');
                } catch (error) {
                    console.error('获取文档列表失败:', error);
                    ElMessage.error('获取文档列表失败');
                    return { records: [], total: 0 };
                }
            },
            
            async uploadDocument(formData) {
                try {
                    const response = await fetch(`${API_BASE}/rag-document/upload`, {
                        method: 'POST',
                        body: formData
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '上传文档失败');
                } catch (error) {
                    console.error('上传文档失败:', error);
                    throw error;
                }
            },
            
            async downloadDocument(documentId) {
                try {
                    const response = await fetch(`${API_BASE}/rag-document/download/${documentId}`, {
                        method: 'GET'
                    });
                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `document_${documentId}`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    } else {
                        throw new Error('下载失败');
                    }
                } catch (error) {
                    console.error('下载文档失败:', error);
                    ElMessage.error('下载文档失败');
                }
            },
            
            async deleteDocument(documentId) {
                try {
                    const response = await fetch(`${API_BASE}/rag-document/delete?id=${documentId}`, {
                        method: 'POST'
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result.data;
                    }
                    throw new Error(result.message || '删除文档失败');
                } catch (error) {
                    console.error('删除文档失败:', error);
                    throw error;
                }
            },

            // Milvus Collections API
            async getCollections() {
                try {
                    const response = await fetch(`${API_BASE}/milvus/collections`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    const result = await response.json();
                    if (result.code === '200') {
                        return result;
                    }
                    throw new Error(result.message || '获取Collections失败');
                } catch (error) {
                    console.error('获取Collections失败:', error);
                    throw error;
                }
            }
        };
        
        // 业务方法
        const loadKnowledgeBases = async () => {
            const data = await api.getKnowledgeBases();
            knowledgeBases.value = data;
            categories.value = data; // 将知识库作为类目显示
            if (data.length > 0 && !selectedCategory.value) {
                selectedCategory.value = data[0];
                await loadDocuments();
            }
        };
        
        const loadDocuments = async () => {
            loading.value = true;
            try {
                const params = {};
                if (selectedCategory.value) {
                    params.categoryId = selectedCategory.value.id;
                }
                if (searchKeyword.value && searchKeyword.value.trim()) {
                    params.fileName = searchKeyword.value.trim();
                }
                if (filterType.value) {
                    params.fileType = filterType.value;
                }

                console.log('查询参数:', params); // 调试日志
                const result = await api.getDocuments(params);
                documents.value = result.records || [];
                totalDocuments.value = result.total || 0;
            } finally {
                loading.value = false;
            }
        };
        
        const selectCategory = (category) => {
            selectedCategory.value = category;
            currentPage.value = 1;
            loadDocuments();
        };
        
        const addCategory = async () => {
            if (!categoryForm.name.trim()) {
                ElMessage.warning('请输入类目名称');
                return;
            }

            try {
                const data = {
                    name: categoryForm.name,
                    description: categoryForm.description,
                    dataType: '00', // 非结构化文档（PDF等）
                    embeddingModel: 'text-embedding-ada-002',
                    topK: 5,
                    similarityThreshold: 0.7,
                    collectionName: `knowledge_${Date.now()}`,
                    requeryOpen: '0', // 不使用改写上下文
                    rerankOpen: '0'   // 不使用rerank
                };

                await api.addKnowledgeBase(data);
                ElMessage.success('添加类目成功');
                showAddCategoryDialog.value = false;
                categoryForm.name = '';
                categoryForm.description = '';
                await loadKnowledgeBases();
            } catch (error) {
                ElMessage.error(error.message || '添加类目失败');
            }
        };
        
        const editCategory = (category) => {
            editCategoryForm.id = category.id;
            editCategoryForm.name = category.name;
            editCategoryForm.description = category.description;
            showEditCategoryDialog.value = true;
        };
        
        const updateCategory = async () => {
            if (!editCategoryForm.name.trim()) {
                ElMessage.warning('请输入类目名称');
                return;
            }
            
            try {
                await api.updateKnowledgeBase(editCategoryForm);
                ElMessage.success('更新类目成功');
                showEditCategoryDialog.value = false;
                await loadKnowledgeBases();
            } catch (error) {
                ElMessage.error(error.message || '更新类目失败');
            }
        };
        
        const deleteCategory = async (category) => {
            try {
                await ElMessageBox.confirm(
                    `确定要删除类目"${category.name}"吗？`,
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                await api.deleteKnowledgeBase(category.id);
                ElMessage.success('删除类目成功');
                await loadKnowledgeBases();
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error(error.message || '删除类目失败');
                }
            }
        };
        
        const handleFileChange = (file, fileListParam) => {
            // 文件大小检查 (50MB)
            if (file.size > 50 * 1024 * 1024) {
                ElMessage.error('文件大小不能超过 50MB');
                fileList.value = [];
                return false;
            }

            // 文件类型检查
            const allowedTypes = ['pdf', 'doc', 'docx', 'txt'];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                ElMessage.error('只支持 PDF、Word、TXT 格式的文件');
                fileList.value = [];
                return false;
            }

            // 更新文件列表
            fileList.value = fileListParam;
            return true;
        };
        
        const uploadDocument = async () => {
            if (!uploadForm.categoryId) {
                ElMessage.warning('请选择类目');
                return;
            }
            if (!uploadForm.ragKnowledgeId) {
                ElMessage.warning('请选择知识库');
                return;
            }
            if (fileList.value.length === 0 || !fileList.value[0].raw) {
                ElMessage.warning('请选择要上传的文件');
                return;
            }

            uploading.value = true;
            try {
                const formData = new FormData();
                formData.append('categoryId', uploadForm.categoryId);
                formData.append('ragKnowledgeId', uploadForm.ragKnowledgeId);
                formData.append('file', fileList.value[0].raw);
                // 添加默认的分片参数
                formData.append('chunkStrategy', '00'); // 智能切分
                formData.append('chunkLength', '1000'); // 默认分段长度

                await api.uploadDocument(formData);
                ElMessage.success('上传文档成功');
                showUploadDialog.value = false;
                fileList.value = [];
                uploadForm.categoryId = null;
                uploadForm.ragKnowledgeId = null;
                await loadDocuments();
            } catch (error) {
                ElMessage.error(error.message || '上传文档失败');
            } finally {
                uploading.value = false;
            }
        };
        
        const downloadDocument = async (document) => {
            await api.downloadDocument(document.id);
        };
        
        const deleteDocument = async (document) => {
            try {
                await ElMessageBox.confirm(
                    `确定要删除文档"${document.fileName}"吗？`,
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                
                await api.deleteDocument(document.id);
                ElMessage.success('删除文档成功');
                await loadDocuments();
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error(error.message || '删除文档失败');
                }
            }
        };
        
        const searchDocuments = () => {
            currentPage.value = 1;
            loadDocuments();
        };
        
        const refreshDocuments = () => {
            searchKeyword.value = '';
            filterType.value = '';
            currentPage.value = 1;
            loadDocuments();
        };
        
        const handleSelectionChange = (selection) => {
            selectedDocuments.value = selection;
        };
        
        const handleSizeChange = (size) => {
            pageSize.value = size;
            currentPage.value = 1;
            loadDocuments();
        };
        
        const handleCurrentChange = (page) => {
            currentPage.value = page;
            loadDocuments();
        };
        
        // 工具函数
        const formatFileSize = (size) => {
            if (!size) return '0 B';
            const units = ['B', 'KB', 'MB', 'GB'];
            let index = 0;
            while (size >= 1024 && index < units.length - 1) {
                size /= 1024;
                index++;
            }
            return `${size.toFixed(2)} ${units[index]}`;
        };
        
        const formatDate = (dateString) => {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        };
        
        const getStatusText = (status) => {
            const statusMap = {
                '00': '待解析',
                '01': '已导入完成',
                '02': '解析失败'
            };
            return statusMap[status] || '未知状态';
        };
        
        const getStatusClass = (status) => {
            const classMap = {
                '00': 'status-tag status-processing',
                '01': 'status-tag status-success',
                '02': 'status-tag status-error'
            };
            return classMap[status] || 'status-tag';
        };
        
        // 显示上传对话框时设置默认值
        const showUploadDialogHandler = () => {
            // 设置默认选中当前类目
            if (selectedCategory.value) {
                uploadForm.categoryId = selectedCategory.value.id;
                uploadForm.ragKnowledgeId = selectedCategory.value.id;
            }
            showUploadDialog.value = true;
        };

        // 知识库管理相关方法
        const selectKnowledge = (knowledge) => {
            selectedKnowledge.value = knowledge;
            loadKnowledgeDocuments(knowledge.id);
        };

        const searchKnowledgeBases = () => {
            // 搜索逻辑已在计算属性中实现
        };

        const createKnowledge = async () => {
            try {
                const response = await api.createKnowledge(knowledgeForm);
                ElMessage.success('创建知识库成功');
                showCreateKnowledgeDialog.value = false;
                resetKnowledgeForm();
                await loadKnowledgeBases();
            } catch (error) {
                ElMessage.error(error.message || '创建知识库失败');
            }
        };

        const editKnowledge = (knowledge) => {
            Object.assign(knowledgeForm, knowledge);
            showCreateKnowledgeDialog.value = true;
        };

        const configKnowledge = (knowledge) => {
            // 打开知识库配置对话框
            ElMessage.info('知识库配置功能开发中');
        };

        const deleteKnowledge = async (knowledge) => {
            try {
                await ElMessageBox.confirm(
                    `确定要删除知识库"${knowledge.name}"吗？删除后将无法恢复。`,
                    '确认删除',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                await api.deleteKnowledge(knowledge.id);
                ElMessage.success('删除知识库成功');
                if (selectedKnowledge.value?.id === knowledge.id) {
                    selectedKnowledge.value = null;
                    knowledgeDocuments.value = [];
                }
                await loadKnowledgeBases();
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error(error.message || '删除知识库失败');
                }
            }
        };

        const resetKnowledgeForm = () => {
            Object.assign(knowledgeForm, {
                name: '',
                description: '',
                dataSourceType: 'local',
                chunkStrategy: 'smart',
                maxChunkSize: 1000,
                chunkOverlap: 100,
                embeddingModel: 'text-embedding-v2',
                collectionName: '',
                similarityThreshold: 0.7
            });
        };

        const loadCollections = async () => {
            try {
                const response = await api.getCollections();
                collections.value = response.data || [];
            } catch (error) {
                console.error('加载Collections失败:', error);
                ElMessage.error('加载Collections失败');
            }
        };

        const loadKnowledgeDocuments = async (knowledgeId) => {
            try {
                const response = await api.getKnowledgeDocuments(knowledgeId);
                knowledgeDocuments.value = response.data || [];
            } catch (error) {
                ElMessage.error('加载知识库文档失败');
                knowledgeDocuments.value = [];
            }
        };

        const bindDocuments = async () => {
            if (selectedBindDocuments.value.length === 0) {
                ElMessage.warning('请选择要绑定的文档');
                return;
            }
            try {
                await api.bindDocumentsToKnowledge(
                    selectedKnowledge.value.id,
                    selectedBindDocuments.value.map(doc => doc.id)
                );
                ElMessage.success('绑定文档成功');
                showBindDocumentsDialog.value = false;
                selectedBindDocuments.value = [];
                await loadKnowledgeDocuments(selectedKnowledge.value.id);
            } catch (error) {
                ElMessage.error(error.message || '绑定文档失败');
            }
        };

        const unbindDocument = async (document) => {
            try {
                await ElMessageBox.confirm(
                    `确定要解绑文档"${document.fileName}"吗？`,
                    '确认解绑',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
                await api.unbindDocumentFromKnowledge(selectedKnowledge.value.id, document.id);
                ElMessage.success('解绑文档成功');
                await loadKnowledgeDocuments(selectedKnowledge.value.id);
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error(error.message || '解绑文档失败');
                }
            }
        };

        const processDocument = async (document) => {
            try {
                await api.processKnowledgeDocument(selectedKnowledge.value.id, document.id);
                ElMessage.success('开始重新处理文档');
                await loadKnowledgeDocuments(selectedKnowledge.value.id);
            } catch (error) {
                ElMessage.error(error.message || '处理文档失败');
            }
        };

        const handleBindDocumentSelectionChange = (selection) => {
            selectedBindDocuments.value = selection;
        };

        const getKnowledgeDocCount = (knowledgeId) => {
            // 这里应该从API获取实际的文档数量，暂时返回模拟数据
            return Math.floor(Math.random() * 10) + 1;
        };

        const getKnowledgeDocStatusType = (status) => {
            const typeMap = {
                'processing': 'warning',
                'completed': 'success',
                'failed': 'danger'
            };
            return typeMap[status] || 'info';
        };

        const getKnowledgeDocStatusText = (status) => {
            const textMap = {
                'processing': '处理中',
                'completed': '已完成',
                'failed': '处理失败'
            };
            return textMap[status] || '未知状态';
        };

        // 生命周期
        onMounted(() => {
            loadKnowledgeBases();
        });
        
        return {
            // 响应式数据
            loading,
            uploading,
            activeTab,
            categories,
            selectedCategory,
            showAddCategoryDialog,
            showEditCategoryDialog,
            categoryForm,
            editCategoryForm,
            knowledgeBases,
            selectedKnowledge,
            knowledgeSearchKeyword,
            filteredKnowledgeBases,
            showCreateKnowledgeDialog,
            knowledgeForm,
            knowledgeRules,
            knowledgeDocuments,
            collections,
            showBindDocumentsDialog,
            availableDocuments,
            selectedBindDocuments,
            bindDocumentSearchKeyword,
            bindDocumentCategoryFilter,
            documents,
            selectedDocuments,
            showUploadDialog,
            uploadForm,
            fileList,
            searchKeyword,
            filterType,
            currentPage,
            pageSize,
            totalDocuments,
            processedDocuments,

            // 方法
            selectCategory,
            addCategory,
            editCategory,
            updateCategory,
            deleteCategory,
            selectKnowledge,
            searchKnowledgeBases,
            createKnowledge,
            editKnowledge,
            configKnowledge,
            deleteKnowledge,
            loadCollections,
            bindDocuments,
            unbindDocument,
            processDocument,
            handleBindDocumentSelectionChange,
            getKnowledgeDocCount,
            getKnowledgeDocStatusType,
            getKnowledgeDocStatusText,
            handleFileChange,
            uploadDocument,
            downloadDocument,
            deleteDocument,
            searchDocuments,
            refreshDocuments,
            handleSelectionChange,
            handleSizeChange,
            handleCurrentChange,
            formatFileSize,
            formatDate,
            getStatusText,
            getStatusClass,
            showUploadDialogHandler
        };
    }
});

// 注册图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

app.use(ElementPlus);
app.mount('#app');
