package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 知识库创建请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库创建请求")
public class RagKnowledgeCreateRequest {

    @NotBlank(message = "知识库名称不能为空")
    @Schema(description = "知识库名称", required = true)
    private String name;

    @Schema(description = "知识库描述")
    private String description;

    @Schema(description = "数据源类型（local-本地数据源，external-外部数据源）", defaultValue = "local")
    private String dataSourceType = "local";

    @Schema(description = "切分策略（smart-智能切分，fixed-固定长度）", defaultValue = "smart")
    private String chunkStrategy = "smart";

    @Min(value = 100, message = "最大切片长度不能小于100")
    @Max(value = 4000, message = "最大切片长度不能大于4000")
    @Schema(description = "最大切片长度", defaultValue = "1000")
    private Integer maxChunkSize = 1000;

    @Min(value = 0, message = "切片重叠长度不能小于0")
    @Max(value = 400, message = "切片重叠长度不能大于400")
    @Schema(description = "切片重叠长度", defaultValue = "100")
    private Integer chunkOverlap = 100;

    @Schema(description = "向量化模型", defaultValue = "text-embedding-v2")
    private String embeddingModel = "text-embedding-v2";

    @NotBlank(message = "Collection名称不能为空")
    @Schema(description = "Collection名称", required = true)
    private String collectionName;

    @DecimalMin(value = "0.1", message = "相似度阈值不能小于0.1")
    @DecimalMax(value = "1.0", message = "相似度阈值不能大于1.0")
    @Schema(description = "相似度阈值", defaultValue = "0.7")
    private Float similarityThreshold = 0.7f;

    @Schema(description = "数据类型（00-非结构文档pdf doc ，01-结构化文档 excel）", defaultValue = "00")
    private String dataType = "00";

    @Schema(description = "是否改写上下文（0-不使用 1-使用）", defaultValue = "0")
    private String requeryOpen = "0";

    @Schema(description = "是否使用rerank（0-不使用 1-使用）", defaultValue = "0")
    private String rerankOpen = "0";

    @Schema(description = "重排序模型")
    private String rerankModel;

    @Min(value = 1, message = "召回片段数量不能小于1")
    @Max(value = 20, message = "召回片段数量不能大于20")
    @Schema(description = "召回片段数量", defaultValue = "5")
    private Integer topK = 5;
}
