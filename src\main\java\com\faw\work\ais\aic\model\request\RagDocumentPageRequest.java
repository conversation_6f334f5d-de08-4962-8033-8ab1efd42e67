package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "文档分页查询请求")
public class RagDocumentPageRequest {
    
    @Schema(description = "当前页码", defaultValue = "1")
    private Integer pageNum = 1;
    
    @Schema(description = "每页记录数", defaultValue = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "文件名称（模糊搜索）")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "解析状态")
    private String parseStatus;

    @Schema(description = "向量化状态")
    private String vectorizeStatus;
} 