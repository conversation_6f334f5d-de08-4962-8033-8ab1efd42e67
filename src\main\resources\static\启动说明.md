# 知识管理系统前端页面启动说明

## 快速启动

### 1. 启动后端服务
```bash
# 在项目根目录执行
mvn spring-boot:run
```

或者在IDE中直接运行 `com.faw.work.ais.AiApplication` 主类。

### 2. 访问前端页面
启动成功后，在浏览器中访问以下地址：

- **主页面**: http://localhost:8080/index.html
- **API测试页面**: http://localhost:8080/test.html

## 🔧 最新修复内容

### 已修复的问题
1. ✅ **类目管理编辑和删除功能** - 后端接口已存在且正常工作
2. ✅ **默认非结构化文档类型** - 新建知识库时默认设置为非结构化文档（PDF等）
3. ✅ **文档列表模糊搜索** - 修复了文件名搜索功能，支持模糊匹配
4. ✅ **移除演示模式** - 完全移除演示模式，只使用真实API
5. ✅ **文档上传默认类目** - 上传时自动选择当前选中的类目
6. ✅ **文件选择检测** - 修复了文件上传时的检测逻辑

## 页面功能说明

### 主页面 (index.html)
这是完整的知识管理系统界面，包含：

#### 左侧边栏 - 类目管理
- 查看所有知识库/类目
- 添加新的知识库（点击"+"按钮）
- 编辑知识库信息（鼠标悬停显示编辑按钮）
- 删除知识库（鼠标悬停显示删除按钮）

#### 右侧内容区 - 文档管理
- **搜索栏**: 按文件名搜索文档
- **筛选器**: 按文件类型（PDF、Word、Excel等）筛选
- **文档列表**: 显示选中类目下的所有文档
- **操作功能**:
  - 上传新文档（点击"导入数据"按钮）
  - 下载文档
  - 删除文档
  - 查看文档状态和信息

### API测试页面 (test.html)
用于测试后端API接口是否正常工作：

- **知识库管理测试**: 测试获取列表、添加知识库等功能
- **文档管理测试**: 测试分页查询文档功能
- **系统信息**: 显示当前系统状态和配置信息

## 使用流程

### 第一次使用
1. 访问主页面 http://localhost:8080/index.html
2. 点击左侧的"+"按钮添加第一个知识库
3. 填写知识库名称和描述，点击确定
4. 选择刚创建的知识库
5. 点击"导入数据"上传文档
6. 选择类目、知识库，然后选择文件上传

### 日常使用
1. 在左侧选择要管理的知识库
2. 在右侧查看该知识库下的文档
3. 使用搜索和筛选功能快速找到目标文档
4. 根据需要进行文档的上传、下载、删除操作

## 技术架构

### 前端技术栈
- **Vue 3**: 使用Composition API构建响应式界面
- **Element Plus**: 提供丰富的UI组件
- **原生CSS**: 自定义样式，支持响应式设计

### 后端接口
- **Spring Boot**: 提供RESTful API
- **MyBatis-Plus**: 数据库操作
- **MySQL**: 数据存储

### 主要API接口
```
POST /rag-knowledge/list     # 获取知识库列表
POST /rag-knowledge/add      # 添加知识库
POST /rag-knowledge/update   # 更新知识库
POST /rag-knowledge/delete   # 删除知识库

POST /rag-document/page      # 分页查询文档
POST /rag-document/upload    # 上传文档
GET  /rag-document/download/{id}  # 下载文档
POST /rag-document/delete    # 删除文档
```

## 故障排除

### 1. 页面无法访问
- 确认Spring Boot应用已正常启动
- 检查端口8080是否被占用
- 确认防火墙设置允许访问8080端口

### 2. API调用失败
- 打开浏览器开发者工具查看网络请求
- 检查后端日志是否有错误信息
- 使用test.html页面测试API接口

### 3. 文件上传失败
- 检查文件大小是否超过50MB限制
- 确认文件格式是否支持（PDF、Word、TXT）
- 检查后端存储配置是否正确

### 4. 数据库连接问题
- 检查application.yaml中的数据库配置
- 确认数据库服务是否正常运行
- 验证数据库用户名密码是否正确

## 开发调试

### 前端调试
1. 打开浏览器开发者工具
2. 在Console标签查看JavaScript错误
3. 在Network标签查看API请求响应
4. 修改app.js中的代码进行调试

### 后端调试
1. 在IDE中设置断点
2. 查看控制台日志输出
3. 使用Postman等工具测试API接口

## 注意事项

1. **文件大小限制**: 单个文件最大50MB
2. **支持格式**: PDF、Word、TXT文件
3. **浏览器兼容**: 建议使用Chrome、Firefox、Safari、Edge最新版本
4. **数据安全**: 删除操作不可恢复，请谨慎操作
5. **网络要求**: 需要访问CDN加载Vue和Element Plus资源

## 联系支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查后端应用日志
3. 参考本文档的故障排除部分
4. 联系开发团队获取技术支持
