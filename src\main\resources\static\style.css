/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f7fa;
    color: #303133;
    line-height: 1.6;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部导航栏 */
.header {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    z-index: 100;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
}

.header-nav {
    display: flex;
    gap: 32px;
}

.nav-item {
    color: #606266;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-item:hover {
    color: #409eff;
    background-color: #ecf5ff;
}

.nav-item.active {
    color: #409eff;
    background-color: #ecf5ff;
    font-weight: 600;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
    width: 340px;
    background: #fff;
    border-right: 1px solid #e4e7ed;
    overflow: hidden;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}

.sidebar-tabs {
    height: 100%;
}

.sidebar-tabs .el-tabs__content {
    padding: 0;
    height: calc(100% - 40px);
    overflow-y: auto;
}

.sidebar-tabs .el-tabs__nav-wrap {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-bottom: 1px solid #e4e7ed;
    padding: 8px 16px 0;
}

.sidebar-tabs .el-tabs__nav {
    border: none;
}

.sidebar-tabs .el-tabs__item {
    font-weight: 500;
    color: #606266;
    border: none;
    padding: 12px 20px;
    margin-right: 8px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
}

.sidebar-tabs .el-tabs__item:hover {
    color: #409eff;
    background: rgba(64, 158, 255, 0.1);
}

.sidebar-tabs .el-tabs__item.is-active {
    color: #409eff;
    background: #fff;
    border-bottom: 2px solid #409eff;
    font-weight: 600;
}

.sidebar-tabs .el-tabs__active-bar {
    display: none;
}

.tab-content {
    padding: 24px;
    height: 100%;
}

.sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f2f5;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.category-item {
    padding: 14px 16px;
    margin-bottom: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e4e7ed;
    background: #fff;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 52px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.category-item:hover {
    border-color: #409eff;
    background: #f8f9fa;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
    transform: translateY(-1px);
}

.category-item.active {
    border-color: #409eff;
    background: #ecf5ff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.category-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    flex: 1;
    text-align: left;
    line-height: 1.4;
}

.category-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-item:hover .category-actions {
    opacity: 1;
}

/* 右侧内容区域 */
.content-area {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background-color: #f5f7fa;
}

/* 内容头部 */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: #fff;
    padding: 20px 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-bar {
    display: flex;
    gap: 16px;
    align-items: center;
    flex: 1;
}

.search-bar > span:first-child {
    font-weight: 600;
    color: #303133;
}

.search-bar .el-icon {
    color: #909399;
}

/* 文档表格容器 */
.document-table {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* 状态标签 */
.status-tag {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-success {
    background-color: #f0f9ff;
    color: #10b981;
    border: 1px solid #a7f3d0;
}

.status-processing {
    background-color: #fef3c7;
    color: #f59e0b;
    border: 1px solid #fde68a;
}

.status-error {
    background-color: #fee2e2;
    color: #ef4444;
    border: 1px solid #fecaca;
}

/* 文件图标 */
.file-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 16px;
    height: 16px;
}

/* 对话框样式优化 */
.el-dialog {
    border-radius: 12px;
}

.el-dialog__header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #f0f2f5;
}

.el-dialog__body {
    padding: 24px;
}

.el-dialog__footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #f0f2f5;
}

/* 表单样式优化 */
.el-form-item {
    margin-bottom: 24px;
}

.el-form-item__label {
    font-weight: 500;
    color: #303133;
}

/* 上传组件样式 */
.el-upload {
    width: 100%;
}

.el-upload__tip {
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
}

/* 分页样式 */
.el-pagination {
    justify-content: center;
    padding: 24px 0;
}

/* 表格样式优化 */
.el-table {
    border-radius: 0;
}

.el-table th {
    background-color: #fafbfc;
    color: #303133;
    font-weight: 600;
}

.el-table td {
    border-bottom: 1px solid #f0f2f5;
}

.el-table tr:hover > td {
    background-color: #f5f7fa;
}

/* 按钮样式优化 */
.el-button {
    border-radius: 6px;
    font-weight: 500;
}

.el-button--primary {
    background-color: #409eff;
    border-color: #409eff;
}

.el-button--primary:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
}

/* 输入框样式优化 */
.el-input__wrapper {
    border-radius: 6px;
}

.el-select .el-input__wrapper {
    border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 220px;
    }
    
    .content-area {
        padding: 16px;
    }
    
    .content-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .search-bar {
        flex-wrap: wrap;
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .header-nav {
        display: none;
    }
    
    .sidebar {
        width: 200px;
    }
    
    .search-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

/* 加载状态 */
.el-loading-mask {
    background-color: rgba(255, 255, 255, 0.8);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 知识库管理样式 */
.knowledge-search {
    margin-bottom: 16px;
}

.knowledge-list {
    list-style: none;
}

.knowledge-item {
    padding: 20px;
    margin-bottom: 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e4e7ed;
    background: #fff;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    min-height: 120px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.knowledge-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #409eff, #67c23a);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.knowledge-item:hover {
    border-color: #409eff;
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
    transform: translateY(-2px);
}

.knowledge-item:hover::before {
    opacity: 1;
}

.knowledge-item.active {
    border-color: #409eff;
    background: linear-gradient(135deg, #ecf5ff, #f0f9ff);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
}

.knowledge-item.active::before {
    opacity: 1;
}

.knowledge-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.knowledge-name {
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.knowledge-description {
    font-size: 13px;
    color: #606266;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 12px;
}

.knowledge-meta {
    font-size: 12px;
    color: #909399;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: auto;
}

.knowledge-meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.knowledge-meta-label {
    font-weight: 500;
    color: #606266;
}

.knowledge-meta-value {
    color: #409eff;
    font-weight: 600;
}

.knowledge-actions {
    display: none;
    gap: 8px;
    align-items: flex-start;
    padding-top: 4px;
}

.knowledge-item:hover .knowledge-actions {
    display: flex;
}

.knowledge-actions .el-button {
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 12px;
}

/* 知识库搜索框优化 */
.knowledge-search .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.knowledge-search .el-input__wrapper:hover {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

/* 添加按钮优化 */
.add-knowledge-btn {
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.add-knowledge-btn:hover {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transform: translateY(-1px);
}

/* 知识库详情页面 */
.knowledge-management-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

.knowledge-detail {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.knowledge-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 24px;
    border-bottom: 1px solid #e4e7ed;
    margin-bottom: 24px;
}

.knowledge-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
}

.knowledge-meta {
    font-size: 14px;
    color: #909399;
    display: flex;
    gap: 16px;
}

.knowledge-documents {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.documents-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.documents-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
}

.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

/* 绑定文档对话框 */
.bind-documents-content {
    max-height: 500px;
    overflow-y: auto;
}

.documents-filter {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    align-items: center;
}

/* 知识库配置表单样式 */
.el-form-item__label {
    font-weight: 500;
}

.el-slider {
    margin: 12px 0;
}

.el-divider {
    margin: 24px 0 16px 0;
}

.el-divider__text {
    font-weight: 600;
    color: #303133;
}
