package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库文档查询请求
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "知识库文档查询请求")
public class RagKnowledgeDocumentsRequest {

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long knowledgeId;

    @Schema(description = "页码", defaultValue = "1")
    private Integer pageNum = 1;

    @Schema(description = "每页数量", defaultValue = "10")
    private Integer pageSize = 10;

    @Schema(description = "文件名称（模糊搜索）")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;
}
